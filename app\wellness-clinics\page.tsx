import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"
import { CheckCircle, ArrowRight, Target, TrendingUp, Users, Star, AlertTriangle, Lightbulb } from "lucide-react"
import Link from "next/link"

export default function WellnessClinicsPage() {
  return (
    <div className="min-h-screen bg-background-white">
      {/* Hero Section */}
      <section className="relative pt-20 lg:pt-32 pb-20 overflow-hidden bg-gradient-to-br from-background-white to-white">
        <div className="container mx-auto px-4 max-w-7xl relative z-10">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl lg:text-6xl font-bold text-charcoal mb-6 leading-tight">
              The Predictable Path to Attracting and Retaining High-Value Patients
            </h1>
            <p className="text-xl lg:text-2xl text-charcoal-500 font-medium leading-relaxed">
              Our strategic marketing systems for established wellness clinics eliminate guesswork and ensure consistent patient growth.
            </p>
          </div>
        </div>
      </section>

      {/* Opening Argument */}
      <section className="py-20">
        <div className="container mx-auto px-4 max-w-7xl">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl lg:text-4xl font-bold text-strategy-blue mb-8">
              Your Patient Roster is Full, But Your Growth Has Stalled.
            </h2>
            <p className="text-xl text-charcoal-500 mb-10 leading-relaxed">
              You've built a successful wellness clinic based on exceptional care, but relying on inconsistent referrals
              and word-of-mouth is limiting your true potential. Wellness Marketing Maestros implements sophisticated,
              data-driven marketing systems that create a predictable flow of ideal, high-value patients, allowing you
              to scale your impact without compromising your mission.
            </p>
            <Button size="lg" variant="accent-contrast" asChild>
              <Link href="/contact">
                Map Your Clinic's Growth Potential
                <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
            </Button>
          </div>
        </div>
      </section>

      {/* Challenge Resolution */}
      <section className="py-20 bg-cloud-grey-50">
        <div className="container mx-auto px-4 max-w-7xl">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl lg:text-4xl font-bold text-strategy-blue mb-12 text-center">
              Common Wellness Clinic Growth Challenges We Solve
            </h2>

            <div className="grid md:grid-cols-2 gap-6">
              <Card className="p-6 bg-white border-l-4 border-l-red-500">
                <CardContent className="p-0">
                  <div className="flex items-start space-x-3">
                    <AlertTriangle className="h-6 w-6 text-red-500 mt-1 flex-shrink-0" />
                    <p className="text-charcoal leading-relaxed">
                      An over-reliance on practitioner referrals, creating unpredictable revenue cycles.
                    </p>
                  </div>
                </CardContent>
              </Card>

              <Card className="p-6 bg-white border-l-4 border-l-red-500">
                <CardContent className="p-0">
                  <div className="flex items-start space-x-3">
                    <AlertTriangle className="h-6 w-6 text-red-500 mt-1 flex-shrink-0" />
                    <p className="text-charcoal leading-relaxed">
                      Struggling to attract self-pay patients who value your premium services.
                    </p>
                  </div>
                </CardContent>
              </Card>

              <Card className="p-6 bg-white border-l-4 border-l-red-500">
                <CardContent className="p-0">
                  <div className="flex items-start space-x-3">
                    <AlertTriangle className="h-6 w-6 text-red-500 mt-1 flex-shrink-0" />
                    <p className="text-charcoal leading-relaxed">
                      Wasting budget on generic marketing tactics that don't resonate with your ideal patient.
                    </p>
                  </div>
                </CardContent>
              </Card>

              <Card className="p-6 bg-white border-l-4 border-l-red-500">
                <CardContent className="p-0">
                  <div className="flex items-start space-x-3">
                    <AlertTriangle className="h-6 w-6 text-red-500 mt-1 flex-shrink-0" />
                    <p className="text-charcoal leading-relaxed">
                      Lacking the data and insights to make confident marketing investment decisions.
                    </p>
                  </div>
                </CardContent>
              </Card>

              <Card className="p-6 bg-white border-l-4 border-l-red-500 md:col-span-2">
                <CardContent className="p-0">
                  <div className="flex items-start space-x-3">
                    <AlertTriangle className="h-6 w-6 text-red-500 mt-1 flex-shrink-0" />
                    <p className="text-charcoal leading-relaxed">
                      Watching less-qualified competitors capture market share with more aggressive marketing.
                    </p>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* Customized Solutions */}
      <section className="py-20">
        <div className="container mx-auto px-4 max-w-7xl">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl lg:text-4xl font-bold text-strategy-blue mb-12 text-center">
              Tailored Strategic Solutions for Wellness Clinics
            </h2>

            <div className="space-y-8">
              <Card className="p-8 border-2 hover:border-strategy-blue-200 transition-colors">
                <CardContent className="p-0">
                  <div className="flex items-start space-x-4">
                    <div className="w-12 h-12 bg-strategy-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
                      <Target className="h-6 w-6 text-strategy-blue-600" />
                    </div>
                    <div>
                      <h3 className="text-xl font-bold text-strategy-blue mb-3">
                        <Link href="/growth-audit" className="hover:text-strategy-blue transition-colors">
                          Clinic Growth Audit & Strategic Roadmap
                        </Link>
                      </h3>
                      <p className="text-charcoal-500 leading-relaxed">
                        We begin by diagnosing your specific market position and patient acquisition gaps to build a
                        data-backed plan.
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="p-8 border-2 hover:border-strategy-blue-200 transition-colors">
                <CardContent className="p-0">
                  <div className="flex items-start space-x-4">
                    <div className="w-12 h-12 bg-strategy-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
                      <TrendingUp className="h-6 w-6 text-strategy-blue-600" />
                    </div>
                    <div>
                      <h3 className="text-xl font-bold text-strategy-blue mb-3">Automated Patient Acquisition Systems</h3>
                      <p className="text-charcoal-500 leading-relaxed">
                        We design and build an "always-on" marketing engine to attract your ideal patients predictably.
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="p-8 border-2 hover:border-strategy-blue-200 transition-colors">
                <CardContent className="p-0">
                  <div className="flex items-start space-x-4">
                    <div className="w-12 h-12 bg-strategy-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
                      <Star className="h-6 w-6 text-strategy-blue-600" />
                    </div>
                    <div>
                      <h3 className="text-xl font-bold text-strategy-blue mb-3">Brand & Authority Platform Development</h3>
                      <p className="text-charcoal-500 leading-relaxed">
                        We elevate your clinic's brand to establish you as the undisputed leader in your local market or
                        specialty.
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="p-8 border-2 hover:border-strategy-blue-200 transition-colors">
                <CardContent className="p-0">
                  <div className="flex items-start space-x-4">
                    <div className="w-12 h-12 bg-strategy-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
                      <Users className="h-6 w-6 text-strategy-blue-600" />
                    </div>
                    <div>
                      <h3 className="text-xl font-bold text-strategy-blue mb-3">"Voice of the Patient" Content Strategy</h3>
                      <p className="text-charcoal-500 leading-relaxed">
                        We develop messaging and content that speaks directly to the needs and anxieties of your target
                        patients, building trust before they ever book an appointment.
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* Case Study */}
      <section className="py-20 bg-gradient-to-br from-emerald-50 to-white">
        <div className="container mx-auto px-4 max-w-7xl">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl lg:text-4xl font-bold text-strategy-blue mb-12 text-center">
              Wellness Clinic Success Story: The Finch Wellness Clinic
            </h2>

            <Card className="p-8 bg-white border-2 border-strategy-blue-200">
              <CardContent className="p-0">
                <div className="grid lg:grid-cols-3 gap-8">
                  <div>
                    <div className="flex items-center mb-4">
                      <AlertTriangle className="h-6 w-6 text-red-500 mr-2" />
                      <h3 className="text-xl font-bold text-charcoal">Problem</h3>
                    </div>
                    <p className="text-charcoal-500 leading-relaxed">
                      A highly-regarded multi-practitioner clinic was hitting a revenue ceiling due to its dependence on
                      inconsistent professional referrals.
                    </p>
                  </div>

                  <div>
                    <div className="flex items-center mb-4">
                      <Lightbulb className="h-6 w-6 text-strategy-blue mr-2" />
                      <h3 className="text-xl font-bold text-charcoal">Solution</h3>
                    </div>
                    <p className="text-charcoal-500 leading-relaxed">
                      We conducted a Growth Audit to identify their ideal, high-value patient profile. We then
                      implemented a Core Project to build an automated patient acquisition system focused on educational
                      content and targeted local outreach.
                    </p>
                  </div>

                  <div>
                    <div className="flex items-center mb-4">
                      <TrendingUp className="h-6 w-6 text-strategy-blue mr-2" />
                      <h3 className="text-xl font-bold text-charcoal">Results</h3>
                    </div>
                    <div className="space-y-3">
                      <div className="flex items-center">
                        <CheckCircle className="h-5 w-5 text-strategy-blue mr-2 flex-shrink-0" />
                        <span className="text-charcoal-500 text-sm">40% increase in qualified new patient bookings</span>
                      </div>
                      <div className="flex items-center">
                        <CheckCircle className="h-5 w-5 text-strategy-blue mr-2 flex-shrink-0" />
                        <span className="text-charcoal-500 text-sm">25% reduction in marketing cost-per-acquisition</span>
                      </div>
                      <div className="flex items-center">
                        <CheckCircle className="h-5 w-5 text-strategy-blue mr-2 flex-shrink-0" />
                        <span className="text-charcoal-500 text-sm">Results achieved within six months</span>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* FAQ */}
      <section className="py-20">
        <div className="container mx-auto px-4 max-w-7xl">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl lg:text-4xl font-bold text-strategy-blue mb-12 text-center">
              Your Questions About Clinic Growth, Answered
            </h2>

            <Accordion type="single" collapsible className="w-full">
              <AccordionItem value="item-1" className="border-b border-cloud-grey">
                <AccordionTrigger className="text-left text-xl font-bold text-charcoal hover:text-strategy-blue">
                  Our clinic is already successful. Why do we need this?
                </AccordionTrigger>
                <AccordionContent className="text-charcoal-500 leading-relaxed">
                  Success often creates complexity. Our systems are designed for established clinics looking to move
                  from incremental growth to predictable, scalable expansion and true market leadership. We build
                  assets, not just run campaigns.
                </AccordionContent>
              </AccordionItem>

              <AccordionItem value="item-2" className="border-b border-cloud-grey">
                <AccordionTrigger className="text-left text-xl font-bold text-charcoal hover:text-strategy-blue">
                  We've been burned by marketing agencies before. How are you different?
                </AccordionTrigger>
                <AccordionContent className="text-charcoal-500 leading-relaxed">
                  We are a consultancy, not an agency. Our "Strategy-First, Implementation-Second" model, beginning with
                  a paid diagnostic, ensures we are fully aligned on goals and methodology before any major investment
                  is made. You receive a complete strategic plan as the first deliverable.
                </AccordionContent>
              </AccordionItem>

              <AccordionItem value="item-3" className="border-b border-cloud-grey">
                <AccordionTrigger className="text-left text-xl font-bold text-charcoal hover:text-strategy-blue">
                  Is this going to be disruptive to our practice?
                </AccordionTrigger>
                <AccordionContent className="text-charcoal-500 leading-relaxed">
                  Our engagement model is designed for busy practice leaders. We handle the strategic heavy lifting,
                  providing clear roadmaps and concise reporting so you can remain focused on patient care while we
                  focus on growth.
                </AccordionContent>
              </AccordionItem>

              <AccordionItem value="item-4" className="border-b border-cloud-grey">
                <AccordionTrigger className="text-left text-xl font-bold text-charcoal hover:text-strategy-blue">
                  How do you know what our patients want?
                </AccordionTrigger>
                <AccordionContent className="text-charcoal-500 leading-relaxed">
                  Our proprietary "Voice of the Patient" Intelligence Engine gives us unparalleled insight into the
                  wellness consumer. We leverage real-world data to craft strategies that resonate deeply and drive
                  action, removing guesswork from your marketing.
                </AccordionContent>
              </AccordionItem>
            </Accordion>
          </div>
        </div>
      </section>

      {/* Final CTA */}
      <section className="py-20 bg-gradient-to-br from-charcoal to-charcoal-800 text-white">
        <div className="container mx-auto px-4 max-w-7xl">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl lg:text-4xl font-bold text-white mb-12">Ready to Scale Your Wellness Clinic?</h2>
            <p className="text-xl text-cloud-grey-100 mb-8 leading-relaxed">
              Stop relying on unpredictable referrals and start building a systematic approach to patient acquisition.
              Let's create a predictable growth engine for your clinic.
            </p>
            <Button size="lg" variant="accent-contrast" className="w-fit" asChild>
              <Link href="/growth-audit">
                Get Your Growth Audit
                <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
            </Button>
          </div>
        </div>
      </section>
    </div>
  )
}
