const { chromium } = require('@playwright/test');
const fs = require('fs');
const path = require('path');

// Mobile breakpoints to test
const breakpoints = [
  { name: 'iPhone SE', width: 320, height: 568 },
  { name: 'iPhone 12/13 mini', width: 375, height: 812 },
  { name: 'iPhone 12/13/14', width: 414, height: 896 },
  { name: 'iPad Portrait', width: 768, height: 1024 },
];

// Pages to audit
const pages = [
  { name: 'Homepage', url: '/' },
  { name: 'About', url: '/about' },
  { name: 'Contact', url: '/contact' },
  { name: 'Growth Audit', url: '/growth-audit' },
  { name: 'Core Implementation', url: '/core-implementation' },
  { name: 'Wellness Clinics', url: '/wellness-clinics' },
  { name: 'Medical Spas', url: '/medical-spas' },
  { name: 'Holistic Functional Medicine', url: '/holistic-functional-medicine' },
  { name: 'Multi-practitioner Centers', url: '/multi-practitioner-centers' },
];

const baseURL = 'http://localhost:3000';

async function auditMobileResponsiveness() {
  const browser = await chromium.launch();
  const context = await browser.newContext();
  const page = await context.newPage();
  
  const results = {
    summary: {
      totalPages: pages.length,
      totalBreakpoints: breakpoints.length,
      totalTests: pages.length * breakpoints.length,
      issues: []
    },
    detailedResults: []
  };

  // Create screenshots directory
  const screenshotsDir = path.join(__dirname, 'mobile-audit-screenshots');
  if (!fs.existsSync(screenshotsDir)) {
    fs.mkdirSync(screenshotsDir, { recursive: true });
  }

  console.log('Starting mobile responsiveness audit...\n');

  for (const pageInfo of pages) {
    console.log(`Auditing page: ${pageInfo.name} (${pageInfo.url})`);
    
    const pageResults = {
      pageName: pageInfo.name,
      url: pageInfo.url,
      breakpointResults: []
    };

    for (const breakpoint of breakpoints) {
      console.log(`  Testing breakpoint: ${breakpoint.name} (${breakpoint.width}px)`);
      
      // Set viewport
      await page.setViewportSize({ 
        width: breakpoint.width, 
        height: breakpoint.height 
      });

      try {
        // Navigate to page
        await page.goto(`${baseURL}${pageInfo.url}`, { 
          waitUntil: 'networkidle', 
          timeout: 30000 
        });

        // Wait for any animations or dynamic content
        await page.waitForTimeout(2000);

        const breakpointResult = {
          breakpointName: breakpoint.name,
          width: breakpoint.width,
          height: breakpoint.height,
          issues: [],
          screenshot: null
        };

        // Take screenshot
        const screenshotPath = path.join(screenshotsDir, `${pageInfo.name.replace(/[^a-zA-Z0-9]/g, '-')}-${breakpoint.width}px.png`);
        await page.screenshot({ 
          path: screenshotPath, 
          fullPage: true 
        });
        breakpointResult.screenshot = screenshotPath;

        // Check for horizontal scrolling
        const bodyScrollWidth = await page.evaluate(() => document.body.scrollWidth);
        const viewportWidth = breakpoint.width;
        if (bodyScrollWidth > viewportWidth) {
          const issue = {
            type: 'horizontal-scroll',
            severity: 'high',
            description: `Page has horizontal scrolling (content width: ${bodyScrollWidth}px, viewport: ${viewportWidth}px)`
          };
          breakpointResult.issues.push(issue);
          results.summary.issues.push({
            page: pageInfo.name,
            breakpoint: breakpoint.name,
            ...issue
          });
        }

        // Check for elements that overflow their containers
        const overflowingElements = await page.evaluate(() => {
          const elements = Array.from(document.querySelectorAll('*'));
          const overflowing = [];
          
          elements.forEach(el => {
            if (el.scrollWidth > el.clientWidth || el.scrollHeight > el.clientHeight) {
              const rect = el.getBoundingClientRect();
              if (rect.width > 0 && rect.height > 0) {
                overflowing.push({
                  tagName: el.tagName,
                  className: el.className,
                  id: el.id,
                  scrollWidth: el.scrollWidth,
                  clientWidth: el.clientWidth,
                  scrollHeight: el.scrollHeight,
                  clientHeight: el.clientHeight
                });
              }
            }
          });
          
          return overflowing.slice(0, 10); // Limit to first 10 to avoid noise
        });

        overflowingElements.forEach(el => {
          const issue = {
            type: 'element-overflow',
            severity: 'medium',
            description: `Element overflows container: ${el.tagName}${el.className ? '.' + el.className.split(' ').join('.') : ''}${el.id ? '#' + el.id : ''} (scroll: ${el.scrollWidth}x${el.scrollHeight}, client: ${el.clientWidth}x${el.clientHeight})`
          };
          breakpointResult.issues.push(issue);
          results.summary.issues.push({
            page: pageInfo.name,
            breakpoint: breakpoint.name,
            ...issue
          });
        });

        // Check for text readability issues (font size too small)
        const smallTextElements = await page.evaluate(() => {
          const elements = Array.from(document.querySelectorAll('*'));
          const smallText = [];
          
          elements.forEach(el => {
            const style = window.getComputedStyle(el);
            const fontSize = parseFloat(style.fontSize);
            const hasText = el.textContent && el.textContent.trim().length > 0;
            
            if (hasText && fontSize < 14 && fontSize > 0) {
              smallText.push({
                tagName: el.tagName,
                className: el.className,
                id: el.id,
                fontSize: fontSize,
                textContent: el.textContent.trim().substring(0, 50)
              });
            }
          });
          
          return smallText.slice(0, 5); // Limit to avoid noise
        });

        smallTextElements.forEach(el => {
          const issue = {
            type: 'small-text',
            severity: 'medium',
            description: `Text too small for mobile: ${el.tagName}${el.className ? '.' + el.className.split(' ').join('.') : ''}${el.id ? '#' + el.id : ''} (${el.fontSize}px) - "${el.textContent}"`
          };
          breakpointResult.issues.push(issue);
          results.summary.issues.push({
            page: pageInfo.name,
            breakpoint: breakpoint.name,
            ...issue
          });
        });

        // Check for buttons that might be too small for touch
        const smallButtons = await page.evaluate(() => {
          const buttons = Array.from(document.querySelectorAll('button, a, input[type="submit"], input[type="button"]'));
          const smallButtons = [];
          
          buttons.forEach(btn => {
            const rect = btn.getBoundingClientRect();
            const isVisible = rect.width > 0 && rect.height > 0;
            const isTooSmall = rect.width < 44 || rect.height < 44; // Apple's recommended minimum touch target
            
            if (isVisible && isTooSmall) {
              smallButtons.push({
                tagName: btn.tagName,
                className: btn.className,
                id: btn.id,
                width: Math.round(rect.width),
                height: Math.round(rect.height),
                textContent: btn.textContent ? btn.textContent.trim().substring(0, 30) : ''
              });
            }
          });
          
          return smallButtons;
        });

        smallButtons.forEach(btn => {
          const issue = {
            type: 'small-touch-target',
            severity: 'high',
            description: `Touch target too small: ${btn.tagName}${btn.className ? '.' + btn.className.split(' ').join('.') : ''}${btn.id ? '#' + btn.id : ''} (${btn.width}x${btn.height}px) - "${btn.textContent}"`
          };
          breakpointResult.issues.push(issue);
          results.summary.issues.push({
            page: pageInfo.name,
            breakpoint: breakpoint.name,
            ...issue
          });
        });

        // Check for images that don't scale properly
        const problematicImages = await page.evaluate(() => {
          const images = Array.from(document.querySelectorAll('img'));
          const problems = [];
          
          images.forEach(img => {
            const rect = img.getBoundingClientRect();
            const naturalWidth = img.naturalWidth;
            const displayWidth = rect.width;
            
            if (naturalWidth > 0 && displayWidth > 0) {
              // Check if image is much larger than container (possible performance issue)
              if (naturalWidth > displayWidth * 2) {
                problems.push({
                  type: 'oversized-image',
                  src: img.src,
                  naturalWidth: naturalWidth,
                  displayWidth: Math.round(displayWidth),
                  alt: img.alt || ''
                });
              }
              
              // Check if image extends beyond viewport
              if (rect.right > window.innerWidth) {
                problems.push({
                  type: 'image-overflow',
                  src: img.src,
                  displayWidth: Math.round(displayWidth),
                  viewportWidth: window.innerWidth,
                  alt: img.alt || ''
                });
              }
            }
          });
          
          return problems;
        });

        problematicImages.forEach(img => {
          const issue = {
            type: img.type,
            severity: img.type === 'image-overflow' ? 'high' : 'medium',
            description: img.type === 'oversized-image' 
              ? `Image much larger than display size: ${img.src.split('/').pop()} (natural: ${img.naturalWidth}px, display: ${img.displayWidth}px)`
              : `Image overflows viewport: ${img.src.split('/').pop()} (width: ${img.displayWidth}px, viewport: ${img.viewportWidth}px)`
          };
          breakpointResult.issues.push(issue);
          results.summary.issues.push({
            page: pageInfo.name,
            breakpoint: breakpoint.name,
            ...issue
          });
        });

        pageResults.breakpointResults.push(breakpointResult);
        
        console.log(`    Found ${breakpointResult.issues.length} issues`);

      } catch (error) {
        console.error(`    Error testing ${pageInfo.name} at ${breakpoint.name}:`, error.message);
        
        const errorResult = {
          breakpointName: breakpoint.name,
          width: breakpoint.width,
          height: breakpoint.height,
          issues: [{
            type: 'page-error',
            severity: 'critical',
            description: `Failed to load or test page: ${error.message}`
          }],
          screenshot: null
        };
        
        pageResults.breakpointResults.push(errorResult);
        results.summary.issues.push({
          page: pageInfo.name,
          breakpoint: breakpoint.name,
          type: 'page-error',
          severity: 'critical',
          description: `Failed to load or test page: ${error.message}`
        });
      }
    }
    
    results.detailedResults.push(pageResults);
    console.log(`Completed ${pageInfo.name}\n`);
  }

  await browser.close();
  
  // Generate report
  const reportPath = path.join(__dirname, 'mobile-audit-report.json');
  fs.writeFileSync(reportPath, JSON.stringify(results, null, 2));
  
  return results;
}

// Generate human-readable report
function generateHtmlReport(results) {
  const reportPath = path.join(__dirname, 'mobile-audit-report.html');
  
  const issuesByPage = {};
  results.summary.issues.forEach(issue => {
    if (!issuesByPage[issue.page]) {
      issuesByPage[issue.page] = {};
    }
    if (!issuesByPage[issue.page][issue.breakpoint]) {
      issuesByPage[issue.page][issue.breakpoint] = [];
    }
    issuesByPage[issue.page][issue.breakpoint].push(issue);
  });

  const severityColors = {
    critical: '#dc2626',
    high: '#ea580c',
    medium: '#ca8a04',
    low: '#65a30d'
  };

  let html = `
<!DOCTYPE html>
<html>
<head>
    <title>Mobile Responsiveness Audit Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .summary { background: #f3f4f6; padding: 20px; border-radius: 8px; margin-bottom: 30px; }
        .page-section { margin-bottom: 40px; border-bottom: 1px solid #e5e7eb; padding-bottom: 20px; }
        .breakpoint { margin-left: 20px; margin-bottom: 20px; }
        .issue { margin: 10px 0; padding: 10px; border-left: 4px solid; border-radius: 4px; background: #f9fafb; }
        .issue.critical { border-color: ${severityColors.critical}; background: #fef2f2; }
        .issue.high { border-color: ${severityColors.high}; background: #fff7ed; }
        .issue.medium { border-color: ${severityColors.medium}; background: #fefce8; }
        .issue.low { border-color: ${severityColors.low}; background: #f0fdf4; }
        .screenshot { max-width: 200px; border: 1px solid #d1d5db; margin: 10px 0; }
        h1, h2, h3 { color: #111827; }
        .no-issues { color: #059669; font-weight: bold; }
    </style>
</head>
<body>
    <h1>Mobile Responsiveness Audit Report</h1>
    
    <div class="summary">
        <h2>Summary</h2>
        <p><strong>Total Pages Tested:</strong> ${results.summary.totalPages}</p>
        <p><strong>Total Breakpoints:</strong> ${results.summary.totalBreakpoints}</p>
        <p><strong>Total Tests:</strong> ${results.summary.totalTests}</p>
        <p><strong>Total Issues Found:</strong> ${results.summary.issues.length}</p>
        
        <h3>Issues by Severity</h3>
        <ul>
            <li><span style="color: ${severityColors.critical}">Critical:</span> ${results.summary.issues.filter(i => i.severity === 'critical').length}</li>
            <li><span style="color: ${severityColors.high}">High:</span> ${results.summary.issues.filter(i => i.severity === 'high').length}</li>
            <li><span style="color: ${severityColors.medium}">Medium:</span> ${results.summary.issues.filter(i => i.severity === 'medium').length}</li>
            <li><span style="color: ${severityColors.low}">Low:</span> ${results.summary.issues.filter(i => i.severity === 'low').length}</li>
        </ul>
    </div>
`;

  results.detailedResults.forEach(pageResult => {
    html += `
    <div class="page-section">
        <h2>${pageResult.pageName} (${pageResult.url})</h2>
`;

    pageResult.breakpointResults.forEach(breakpointResult => {
      html += `
        <div class="breakpoint">
            <h3>${breakpointResult.breakpointName} (${breakpointResult.width}px)</h3>
`;

      if (breakpointResult.screenshot) {
        html += `<img src="${breakpointResult.screenshot}" alt="Screenshot" class="screenshot">`;
      }

      if (breakpointResult.issues.length === 0) {
        html += `<p class="no-issues">No issues found!</p>`;
      } else {
        breakpointResult.issues.forEach(issue => {
          html += `<div class="issue ${issue.severity}"><strong>${issue.type.toUpperCase()}:</strong> ${issue.description}</div>`;
        });
      }

      html += `</div>`;
    });

    html += `</div>`;
  });

  html += `
</body>
</html>`;

  fs.writeFileSync(reportPath, html);
  console.log(`HTML report generated: ${reportPath}`);
}

// Run the audit
(async () => {
  try {
    console.log('Mobile Responsiveness Audit Starting...\n');
    const results = await auditMobileResponsiveness();
    
    console.log('\n=== AUDIT COMPLETE ===');
    console.log(`Total issues found: ${results.summary.issues.length}`);
    
    // Group issues by severity
    const bySeverity = results.summary.issues.reduce((acc, issue) => {
      acc[issue.severity] = (acc[issue.severity] || 0) + 1;
      return acc;
    }, {});
    
    console.log('Issues by severity:');
    Object.entries(bySeverity).forEach(([severity, count]) => {
      console.log(`  ${severity}: ${count}`);
    });
    
    generateHtmlReport(results);
    
    console.log('\nReport files generated:');
    console.log('- mobile-audit-report.json (detailed JSON data)');
    console.log('- mobile-audit-report.html (human-readable report)');
    console.log('- mobile-audit-screenshots/ (screenshots directory)');
    
  } catch (error) {
    console.error('Audit failed:', error);
    process.exit(1);
  }
})();