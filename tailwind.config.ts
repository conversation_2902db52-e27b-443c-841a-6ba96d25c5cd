import type { Config } from "tailwindcss";
import plugin from "tailwindcss/plugin";

const config: Config = {
    darkMode: ["class"],
    content: [
    "./pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./components/**/*.{js,ts,jsx,tsx,mdx}",
    "./app/**/*.{js,ts,jsx,tsx,mdx}",
    "*.{js,ts,jsx,tsx,mdx}",
    // Exclude test files and stories for smaller bundle
    "!./components/**/*.{test,spec,stories}.{js,ts,jsx,tsx}",
    "!./node_modules/**",
  ],
  // Enable future optimizations
  future: {
    hoverOnlyWhenSupported: true,
  },
  theme: {
  	extend: {
  		colors: {
  			// Brand Colors - Wellness Marketing Maestros
  			'strategy-blue': {
  				DEFAULT: '#5971E8',
  				50: '#F0F2FE',
  				100: '#E1E6FD',
  				200: '#C9D2FB',
  				300: '#A4B5F7',
  				400: '#7B91F1',
  				500: '#5971E8',
  				600: '#3B52DC',
  				700: '#2D3FC9',
  				800: '#2A35A3',
  				900: '#283181',
  			},
  			'charcoal': {
  				DEFAULT: '#333333',
  				50: '#F5F5F5',
  				100: '#E8E8E8',
  				200: '#D1D1D1',
  				300: '#B4B4B4',
  				400: '#999999',
  				500: '#666666',
  				600: '#4D4D4D',
  				700: '#333333',
  				800: '#1A1A1A',
  				900: '#0D0D0D',
  			},
  			'insight-gold': {
  				DEFAULT: '#E8B95A',
  				50: '#FDF8F0',
  				100: '#FBF0E1',
  				200: '#F7E0C3',
  				300: '#F1CCA0',
  				400: '#EDB77D',
  				500: '#E8B95A',
  				600: '#E09A37',
  				700: '#C17F1F',
  				800: '#9D6619',
  				900: '#7A4F13',
  			},
  			'cloud-grey': {
  				DEFAULT: '#EAEAEA',
  				50: '#FAFAFA',
  				100: '#F5F5F5',
  				200: '#EAEAEA',
  				300: '#D4D4D4',
  				400: '#A3A3A3',
  				500: '#737373',
  				600: '#525252',
  				700: '#404040',
  				800: '#262626',
  				900: '#171717',
  			},
  			'background-white': '#FBFBFB',

  			// Semantic color mappings
  			background: 'rgb(var(--background))',
  			foreground: 'rgb(var(--foreground))',
  			card: {
  				DEFAULT: 'rgb(var(--card))',
  				foreground: 'rgb(var(--card-foreground))'
  			},
  			popover: {
  				DEFAULT: 'rgb(var(--popover))',
  				foreground: 'rgb(var(--popover-foreground))'
  			},
  			primary: {
  				DEFAULT: 'rgb(var(--primary))',
  				foreground: 'rgb(var(--primary-foreground))'
  			},
  			secondary: {
  				DEFAULT: 'rgb(var(--secondary))',
  				foreground: 'rgb(var(--secondary-foreground))'
  			},
  			muted: {
  				DEFAULT: 'rgb(var(--muted))',
  				foreground: 'rgb(var(--muted-foreground))'
  			},
  			accent: {
  				DEFAULT: 'rgb(var(--accent))',
  				foreground: 'rgb(var(--accent-foreground))'
  			},
  			destructive: {
  				DEFAULT: 'rgb(var(--destructive))',
  				foreground: 'rgb(var(--destructive-foreground))'
  			},
  			border: 'rgb(var(--border))',
  			input: 'rgb(var(--input))',
  			ring: 'rgb(var(--ring))',
  			chart: {
  				'1': 'rgb(var(--chart-1))',
  				'2': 'rgb(var(--chart-2))',
  				'3': 'rgb(var(--chart-3))',
  				'4': 'rgb(var(--chart-4))',
  				'5': 'rgb(var(--chart-5))'
  			},
  			sidebar: {
  				DEFAULT: 'rgb(var(--sidebar))',
  				foreground: 'rgb(var(--sidebar-foreground))',
  				primary: 'rgb(var(--sidebar-primary))',
  				'primary-foreground': 'rgb(var(--sidebar-primary-foreground))',
  				accent: 'rgb(var(--sidebar-accent))',
  				'accent-foreground': 'rgb(var(--sidebar-accent-foreground))',
  				border: 'rgb(var(--sidebar-border))',
  				ring: 'rgb(var(--sidebar-ring))'
  			}
  		},
  		borderRadius: {
  			lg: 'var(--radius)',
  			md: 'calc(var(--radius) - 2px)',
  			sm: 'calc(var(--radius) - 4px)'
  		},
  		keyframes: {
  			'accordion-down': {
  				from: {
  					height: '0'
  				},
  				to: {
  					height: 'var(--radix-accordion-content-height)'
  				}
  			},
  			'accordion-up': {
  				from: {
  					height: 'var(--radix-accordion-content-height)'
  				},
  				to: {
  					height: '0'
  				}
  			}
  		},
  		animation: {
  			'accordion-down': 'accordion-down 0.2s ease-out',
  			'accordion-up': 'accordion-up 0.2s ease-out'
  		},
			spacing: {
				'18': '4.5rem',
				'88': '22rem',
				'section': '5rem', // 80px equivalent
				'section-sm': '3rem', // 48px equivalent
				'section-lg': '6rem', // 96px equivalent
			},
			fontSize: {
				'responsive-xl': 'clamp(1.5rem, 4vw, 3rem)',
				'responsive-lg': 'clamp(1.25rem, 3vw, 2.25rem)',
				'responsive-base': 'clamp(1rem, 2.5vw, 1.125rem)',
			},
			lineHeight: {
				'relaxed-plus': '1.75',
			},
			maxWidth: {
				'content': 'min(90%, 1200px)',
			},
  	}
  },
  plugins: [
  require("tailwindcss-animate"),
  plugin(function({ addBase, theme }) {
  	   addBase({
  	     '.prose': {
  	       color: theme('colors.charcoal.500'),
  	       fontSize: theme('fontSize.base'),
  	       lineHeight: theme('lineHeight.relaxed'),
  	       '& p': {
  	         marginBottom: theme('spacing.6'),
  	       },
  	       '& h1, & h2, & h3, & h4, & h5, & h6': {
  	         color: theme('colors.charcoal.700'),
  	         fontWeight: theme('fontWeight.bold'),
  	       },
  	       '& a': {
  	         color: theme('colors.strategy-blue.600'),
  	         textDecoration: 'none',
  	         '&:hover': {
  	           textDecoration: 'underline',
  	         },
  	       },
  	     },
  	     '.prose-lg': {
  	       fontSize: theme('fontSize.lg'),
  	       '& p': {
  	         fontSize: theme('fontSize.lg'),
  	       },
  	     },
  	   })
  	 }),
  require("@tailwindcss/forms"),
  require("@tailwindcss/container-queries"),
 ],
};
export default config;
