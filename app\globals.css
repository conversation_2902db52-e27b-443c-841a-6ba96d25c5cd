@tailwind base;
@tailwind components;
@tailwind utilities;

/* Base layer styles - foundational elements and resets */
@layer base {
  * {
    @apply border-border;
  }
  
  *:focus-visible {
    @apply outline-ring/50;
  }
  
  body {
    background-color: #FBFBFB;
    color: #333333;
    font-feature-settings: "rlig" 1, "calt" 1;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    /* Mobile scroll fix: Use max-width and contain layout instead of overflow-x hidden */
    max-width: 100vw;
    contain: layout;
  }
  
  html {
    scroll-behavior: smooth;
    /* Mobile scroll fix: Only hide horizontal overflow on html, allow body to scroll naturally */
    overflow-x: hidden;
  }
  
  @media (prefers-reduced-motion: reduce) {
    html {
      scroll-behavior: auto;
    }
  }

  /* Typography scale with improved hierarchy */
  h1, h2, h3, h4, h5, h6 {
    line-height: 1.2;
    font-weight: 700;
  }
  
  h1 {
    @apply text-4xl md:text-5xl lg:text-6xl font-bold text-charcoal;
    letter-spacing: -0.02em;
    line-height: 1.2;
    text-wrap: balance; /* Prevent orphaned words and improve line breaks */
  }

  h2 {
    @apply text-2xl md:text-3xl lg:text-4xl font-bold text-strategy-blue;
    letter-spacing: -0.01em;
    line-height: 1.4; /* Increased from 1.3 for better readability */
    text-wrap: balance; /* Prevent orphaned words and improve line breaks */
  }

  h3 {
    @apply text-xl md:text-2xl lg:text-3xl font-bold text-charcoal;
    letter-spacing: -0.01em;
    line-height: 1.3;
    text-wrap: balance;
  }

  h4 {
    @apply text-lg md:text-xl lg:text-2xl font-semibold text-charcoal;
    line-height: 1.4;
    text-wrap: balance;
  }

  h5 {
    @apply text-base md:text-lg lg:text-xl font-semibold text-charcoal;
    line-height: 1.4;
    text-wrap: balance;
  }

  h6 {
    @apply text-sm md:text-base lg:text-lg font-semibold text-charcoal;
    line-height: 1.4;
    text-wrap: balance;
  }

  /* Optimized paragraph styling */
  p {
    @apply leading-relaxed text-base;
    text-wrap: pretty; /* Better line breaking for paragraphs */
    hyphens: none; /* Disable hyphenation on mobile for better readability */
    word-break: normal; /* Prevent aggressive word breaking */
    overflow-wrap: break-word; /* Break long words if necessary */
  }

  /* Allow paragraphs to center properly when in text-center containers */
  .text-center p {
    margin-left: auto;
    margin-right: auto;
  }

  /* Link improvements */
  a {
    @apply transition-colors duration-200;
  }

  a:focus-visible {
    @apply ring-4 ring-strategy-blue ring-offset-2 ring-offset-background rounded-sm;
  }

  /* Improved list styling */
  ul, ol {
    @apply leading-relaxed;
  }

  /* Better image defaults */
  img {
    max-width: 100%;
    height: auto;
  }

  /* Form element improvements */
  input, textarea, select {
    @apply transition-colors duration-200;
  }
}

:root {
  /* Brand Color Palette - Wellness Marketing Maestros */

  /* Primary Colors (25% usage) */
  --strategy-blue: 89 113 232; /* #5971E8 */
  --charcoal: 51 51 51; /* #333333 */

  /* Secondary Colors */
  --insight-gold: 232 185 90; /* #E8B95A - Accent (5% usage) */
  --cloud-grey: 234 234 234; /* #EAEAEA */
  --background-white: 251 251 251; /* #FBFBFB - Primary neutral (70% usage) */
  --white: 255 255 255; /* #FFFFFF */

  /* Semantic color mappings using brand palette */
  --background: var(--background-white);
  --foreground: var(--charcoal);
  --card: var(--white);
  --card-foreground: var(--charcoal);
  --popover: var(--white);
  --popover-foreground: var(--charcoal);
  --primary: var(--strategy-blue);
  --primary-foreground: var(--white);
  --secondary: var(--cloud-grey);
  --secondary-foreground: var(--charcoal);
  --muted: var(--cloud-grey);
  --muted-foreground: 102 102 102; /* Lighter charcoal for muted text */
  --accent: var(--insight-gold);
  --accent-foreground: var(--charcoal);
  /* High contrast accent for better accessibility */
  --accent-contrast: 200 148 52; /* Darker gold for better contrast */
  --destructive: 239 68 68; /* Red for errors */
  --destructive-foreground: var(--white);
  --border: var(--cloud-grey);
  --input: var(--cloud-grey);
  --ring: var(--strategy-blue);
  --chart-1: var(--strategy-blue);
  --chart-2: var(--insight-gold);
  --chart-3: var(--charcoal);
  --chart-4: var(--cloud-grey);
  --chart-5: 168 168 168; /* Medium grey */
  --radius: 0.625rem;
  --sidebar: var(--background-white);
  --sidebar-foreground: var(--charcoal);
  --sidebar-primary: var(--strategy-blue);
  --sidebar-primary-foreground: var(--white);
  --sidebar-accent: var(--cloud-grey);
  --sidebar-accent-foreground: var(--charcoal);
  --sidebar-border: var(--cloud-grey);
  --sidebar-ring: var(--strategy-blue);
}

.dark {
  /* Dark mode using brand colors with appropriate contrast */
  --background: var(--charcoal);
  --foreground: var(--white);
  --card: 26 26 26; /* Darker charcoal for cards */
  --card-foreground: var(--white);
  --popover: 26 26 26;
  --popover-foreground: var(--white);
  --primary: var(--strategy-blue);
  --primary-foreground: var(--white);
  --secondary: 64 64 64; /* Lighter charcoal for secondary */
  --secondary-foreground: var(--white);
  --muted: 64 64 64;
  --muted-foreground: 168 168 168; /* Medium grey for muted text */
  --accent: var(--insight-gold);
  --accent-foreground: var(--charcoal);
  --destructive: 239 68 68;
  --destructive-foreground: var(--white);
  --border: 64 64 64;
  --input: 64 64 64;
  --ring: var(--strategy-blue);
  --chart-1: var(--strategy-blue);
  --chart-2: var(--insight-gold);
  --chart-3: var(--white);
  --chart-4: 168 168 168;
  --chart-5: 128 128 128;
  --sidebar: var(--charcoal);
  --sidebar-foreground: var(--white);
  --sidebar-primary: var(--strategy-blue);
  --sidebar-primary-foreground: var(--white);
  --sidebar-accent: 64 64 64;
  --sidebar-accent-foreground: var(--white);
  --sidebar-border: 64 64 64;
  --sidebar-ring: var(--strategy-blue);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

/* Component layer - reusable component patterns */
@layer components {
  /* Layout Components */
  .container-custom {
    @apply mx-auto px-4 max-w-7xl;
  }

  .section-padding {
    @apply py-8 md:py-12 lg:py-16;
  }

  .section-padding-sm {
    @apply py-8 md:py-12 lg:py-16;
  }

  .section-wrapper {
    @apply py-12 md:py-16 lg:py-20;
  }

  .section-wrapper-sm {
    @apply py-8 md:py-12 lg:py-16;
  }

  .content-container {
    @apply max-w-4xl mx-auto;
  }

  /* Responsive text containers to prevent awkward line breaks */
  .text-container-responsive {
    @apply max-w-2xl md:max-w-3xl lg:max-w-4xl mx-auto;
  }

  .text-container-wide {
    @apply max-w-3xl md:max-w-4xl lg:max-w-5xl mx-auto;
  }

  /* Hero Components */
  .hero-section {
    @apply relative bg-gradient-to-br from-background-white to-white py-20 lg:py-32;
  }

  .hero-content {
    @apply container-custom;
  }

  .hero-text {
    @apply max-w-4xl mx-auto text-center;
  }

  .hero-title {
    @apply text-4xl lg:text-6xl font-bold text-charcoal mb-8 leading-tight;
  }

  .hero-subtitle {
    @apply text-xl text-charcoal-500 mb-10 leading-relaxed;
  }

  /* Card Components */
  .card-base {
    @apply p-6 md:p-8 bg-white rounded-lg border-2 border-cloud-grey;
    @apply transition-all duration-300 hover:border-strategy-blue-200 hover:shadow-lg;
  }

  .card-feature {
    @apply card-base text-center min-h-[300px] flex flex-col;
  }

  .card-icon {
    @apply w-16 h-16 bg-strategy-blue-100 rounded-full flex items-center justify-center mx-auto mb-6;
  }

  /* Button Components */
  .btn-primary {
    @apply bg-insight-gold hover:bg-insight-gold-600 text-charcoal px-8 py-4 text-lg font-semibold;
    @apply transition-all duration-200 hover:shadow-lg active:scale-95;
  }

  .btn-secondary {
    @apply bg-strategy-blue hover:bg-strategy-blue-600 text-white px-8 py-4 text-lg font-semibold;
    @apply transition-all duration-200 hover:shadow-lg active:scale-95;
  }

  /* CTA Components */
  .cta-section {
    @apply py-20 bg-gradient-to-br from-charcoal to-charcoal-800 text-white;
  }

  .cta-content {
    @apply container-custom content-container text-center;
  }

  .cta-title {
    @apply text-3xl lg:text-4xl font-bold text-white mb-12;
  }

  .bonus-card {
    @apply p-8 bg-insight-gold border-insight-gold-600 mt-20 mb-20 rounded-lg;
  }

  /* Process Step Components */
  .process-steps {
    @apply space-y-8;
  }

  .process-step {
    @apply card-base hover:border-strategy-blue-200;
  }

  .process-step-content {
    @apply p-0 flex items-start space-x-4;
  }

  .process-step-number {
    @apply w-12 h-12 bg-strategy-blue-100 rounded-full flex items-center justify-center;
    @apply text-strategy-blue-600 font-bold text-lg flex-shrink-0;
  }

  .process-step-title {
    @apply text-xl font-bold text-strategy-blue mb-3;
  }

  .process-step-description {
    @apply text-charcoal-500 leading-relaxed;
  }

  /* Trust Indicator Components */
  .trust-indicators {
    @apply grid md:grid-cols-3 gap-6 max-w-3xl mx-auto;
  }

  .trust-indicator {
    @apply flex items-center justify-center space-x-3 p-4 bg-strategy-blue-50 rounded-lg;
  }

  .trust-icon {
    @apply h-6 w-6 text-strategy-blue-600;
  }

  .trust-text {
    @apply font-semibold text-charcoal;
  }

  /* Form Components */
  .form-container {
    @apply card-base;
  }

  .form-fieldset {
    @apply space-y-6;
  }

  .form-field {
    @apply space-y-2;
  }

  .form-label {
    @apply text-charcoal-700 font-medium;
  }

  .form-input {
    @apply border-cloud-grey transition-colors duration-200;
  }

  .form-error {
    @apply text-red-500 text-sm;
  }

  /* Marketing Sherpa Card Mobile Fix */
  .marketing-sherpa-card {
    @apply p-8 bg-gradient-to-br from-blue-50 to-white border-strategy-blue-200 hover:shadow-lg transition-all duration-300;
  }

  .marketing-sherpa-content {
    @apply flex items-start space-x-6 mb-6;
  }

  /* Mobile-specific styles for Marketing Sherpa card */
  @media (max-width: 768px) {
    .marketing-sherpa-content {
      @apply flex-col space-x-0 space-y-4 text-center;
    }
    
    .marketing-sherpa-logo {
      @apply mx-auto;
    }
    
    .marketing-sherpa-text {
      @apply text-center;
    }

    /* Footer mobile fixes */
    .footer-logo {
      @apply h-8 w-auto max-w-full; /* Smaller base size for mobile */
    }
    
    .footer-email {
      @apply text-sm break-words;
    }

    /* Additional fixes for very narrow screens (320px) */
    @media (max-width: 320px) {
      .footer-logo {
        @apply h-6 max-w-[200px] object-contain;
      }
      
      .footer-email {
        @apply text-xs break-all leading-tight;
        word-break: break-all;
        overflow-wrap: anywhere;
      }
      
      /* Ensure footer content doesn't overflow */
      .footer-content {
        @apply px-2 overflow-hidden;
      }
      
      /* Reduce footer spacing on very narrow screens */
      .footer-spacing {
        @apply py-8 px-2;
      }
    }
  }

  /* Desktop footer fixes - moved outside mobile media query */
  @media (min-width: 769px) {
    .footer-logo {
      @apply h-7 w-auto max-w-full !important; /* 28px - better visual balance in footer context */
    }
    
    .footer-content {
      @apply px-4;
    }
  }

  /* Mobile Form Enhancements */
  @media (max-width: 768px) {
    .mobile-form-input {
      @apply h-14 text-base; /* Larger touch targets and text on mobile */
    }

    .mobile-form-button {
      @apply h-14 text-base; /* Larger touch targets on mobile */
    }

    /* Ensure all containers respect mobile viewport */
    .container {
      max-width: 100vw;
      overflow-x: hidden;
    }

    /* Prevent any element from exceeding viewport width */
    * {
      max-width: 100vw;
      box-sizing: border-box;
    }

    /* Specific fixes for common overflow elements */
    img, video, iframe {
      max-width: 100% !important;
      height: auto !important;
    }

    /* Mobile button improvements */
    button, .btn, [role="button"] {
      max-width: 100%;
      word-wrap: break-word;
      hyphens: none; /* Disable hyphens for buttons */
      white-space: normal; /* Allow text wrapping */
      text-align: center;
      line-height: 1.4;
      min-height: 3rem; /* Ensure adequate height for wrapped text */
    }

    /* Allow button text to wrap on very small screens */
    @media (max-width: 480px) {
      button, .btn, [role="button"] {
        white-space: normal;
        text-align: center;
        line-height: 1.4;
        padding-top: 0.75rem;
        padding-bottom: 0.75rem;
        min-height: 3.5rem; /* Slightly more height on smaller screens */
      }
    }
  }
}

/* Utilities layer - specific functionality and enhancements */
@layer utilities {
  /* Typography utilities - removed experimental CSS for compatibility */

  /* Screen reader utilities */
  .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
  }
  
  .focus\:not-sr-only:focus {
    position: static;
    width: auto;
    height: auto;
    padding: inherit;
    margin: inherit;
    overflow: visible;
    clip: auto;
    white-space: normal;
  }

  /* Content layout utilities */
  .content-spacing {
    @apply space-y-4 md:space-y-6;
  }

  .mobile-spacing {
    @apply px-4 md:px-6 lg:px-8;
  }

  .optimal-line-length {
    /* Utility class for optimal reading line length - removed fixed max-width */
  }

  /* Improved text wrapping utilities */
  .text-wrap-balance {
    text-wrap: balance;
  }

  .text-wrap-pretty {
    text-wrap: pretty;
  }

  .text-wrap-stable {
    text-wrap: stable;
  }

  /* Prevent awkward line breaks */
  .no-orphans {
    orphans: 2;
    widows: 2;
  }

  /* Better word breaking for long content */
  .break-words-smart {
    word-break: normal;
    overflow-wrap: break-word;
    hyphens: auto;
  }

  /* Enhanced interaction states */
  .hover-lift {
    @apply transition-all duration-300 hover:shadow-xl hover:-translate-y-1;
  }

  .card-hover-enhanced {
    @apply hover-lift hover:border-strategy-blue-300;
  }

  .button-press {
    @apply active:scale-95 transition-transform duration-150;
  }

  /* Focus utilities for accessibility */
  .enhanced-focus:focus-visible {
    @apply ring-4 ring-strategy-blue ring-offset-2 ring-offset-background;
  }

  .focus-visible-only {
    @apply focus-visible:ring-4 focus-visible:ring-strategy-blue focus-visible:ring-offset-2;
  }

  /* Trust and credibility utilities */
  .trust-badge {
    @apply inline-flex items-center px-3 py-1 rounded-full text-sm font-medium;
    @apply bg-strategy-blue-50 text-strategy-blue border border-strategy-blue-200;
  }

  /* Loading and state utilities */
  .loading-spinner {
    @apply w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin;
  }

  /* Accessibility utilities */
  @media (prefers-contrast: high) {
    .text-charcoal-500 {
      @apply text-charcoal;
    }
    
    .border-cloud-grey {
      @apply border-charcoal-300;
    }
  }

  @media (prefers-reduced-motion: reduce) {
    .respect-motion-preference {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }
    
    .respect-motion-preference * {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }
  }

  /* Brand-specific utilities */
  .brand-gradient {
    @apply bg-gradient-to-r from-strategy-blue to-strategy-blue-600;
  }

  .brand-gradient-text {
    @apply brand-gradient bg-clip-text text-transparent;
  }

  .wellness-shadow {
    box-shadow: 0 10px 25px -5px rgba(89, 113, 232, 0.1), 0 10px 10px -5px rgba(89, 113, 232, 0.04);
  }

  .wellness-shadow-lg {
    box-shadow: 0 25px 50px -12px rgba(89, 113, 232, 0.15), 0 25px 25px -5px rgba(89, 113, 232, 0.08);
  }

  /* Form utilities */
  .form-invalid {
    @apply border-red-500 focus:border-red-500 focus:ring-red-500;
  }

  .form-valid {
    @apply border-green-500 focus:border-green-500 focus:ring-green-500;
  }
}

