{"permissions": {"allow": ["Bash(git init:*)", "Bash(npm install:*)", "Bash(npm run dev:*)", "Bash(npm run build:*)", "<PERSON><PERSON>(timeout 10 npm run dev)", "Bash(PORT=3003 npm start)", "Bash(npm run lint)", "Bash(git add:*)", "Bash(git commit:*)", "Bash(git push:*)", "<PERSON><PERSON>(npx playwright:*)", "Bash(start cmd /k \"npm run dev\")", "Bash(node:*)", "Bash(grep:*)", "mcp__playwright__browser_navigate", "mcp__playwright__browser_resize", "mcp__playwright__browser_take_screenshot", "mcp__playwright__browser_evaluate", "mcp__playwright__browser_close", "mcp__sequential-thinking__sequentialthinking"], "deny": []}}