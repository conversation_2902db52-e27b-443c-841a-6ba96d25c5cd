{"timestamp": "2025-07-25T11:58:00.705Z", "summary": {"totalPages": 9, "totalBreakpoints": 4, "fixesVerified": {"marketingSherpaCard": {"pass": 4, "fail": 0, "details": [{"page": "Homepage", "breakpoint": "iPhone SE", "status": "pass", "details": [{"logoIndex": 0, "logoPosition": {"top": 5696.25, "bottom": 5696.25}, "cardPosition": {"top": 5663.25, "bottom": 6932.75}, "isLogoOnTop": true, "textBelowLogo": true, "textElementsFound": 6, "status": "pass"}]}, {"page": "Homepage", "breakpoint": "iPhone 12/13 mini", "status": "pass", "details": [{"logoIndex": 0, "logoPosition": {"top": 5298.5, "bottom": 5298.5}, "cardPosition": {"top": 5265.5, "bottom": 6280.75}, "isLogoOnTop": true, "textBelowLogo": true, "textElementsFound": 6, "status": "pass"}]}, {"page": "Homepage", "breakpoint": "iPhone 12/13/14", "status": "pass", "details": [{"logoIndex": 0, "logoPosition": {"top": 5051.75, "bottom": 5051.75}, "cardPosition": {"top": 5018.75, "bottom": 5949.5}, "isLogoOnTop": true, "textBelowLogo": true, "textElementsFound": 6, "status": "pass"}]}, {"page": "Homepage", "breakpoint": "iPad Portrait", "status": "pass", "details": [{"logoIndex": 0, "logoPosition": {"top": 5340.5625, "bottom": 5340.5625}, "cardPosition": {"top": 5307.5625, "bottom": 5896.3125}, "isLogoOnTop": true, "textBelowLogo": true, "textElementsFound": 6, "status": "pass"}]}]}, "buttonTextWrapping": {"pass": 36, "fail": 0, "details": []}, "wordHyphenation": {"pass": 36, "fail": 0, "details": []}, "footerElements": {"pass": 27, "fail": 9, "details": []}}, "overallStatus": "mostly_fixed"}, "detailedResults": [{"pageName": "Homepage", "url": "/", "breakpointResults": [{"breakpointName": "iPhone SE", "width": 320, "height": 568, "fixVerifications": {"marketingSherpaCard": {"status": "pass", "details": [{"logoIndex": 0, "logoPosition": {"top": 5696.25, "bottom": 5696.25}, "cardPosition": {"top": 5663.25, "bottom": 6932.75}, "isLogoOnTop": true, "textBelowLogo": true, "textElementsFound": 6, "status": "pass"}]}, "buttonTextWrapping": {"status": "pass", "details": ["All 5 buttons wrap text properly"]}, "wordHyphenation": {"status": "pass", "details": ["No unwanted hyphenation found in 177 text elements"]}, "footerElements": {"status": "fail", "details": ["logo-overflow: Logo 1 in footer 1 - overflow detected", "email-overflow: Email 1 in footer 1 - Strategic consultancy for established wellness cli", "email-overflow: Email 2 in footer 1 - Strategic consultancy for established wellness cli", "email-overflow: Email 3 in footer 1 - Strategic consultancy for established wellness cli", "email-overflow: Email 4 in footer 1 - <EMAIL> -", "email-overflow: Email 5 in footer 1 - <EMAIL>", "email-overflow: Email 6 in footer 1 - <EMAIL>"]}}, "screenshot": "C:\\AI Coding Projects\\Clients\\wellness-marketing-maestros\\post-implementation-screenshots\\Homepage-320px-post-fix.png"}, {"breakpointName": "iPhone 12/13 mini", "width": 375, "height": 812, "fixVerifications": {"marketingSherpaCard": {"status": "pass", "details": [{"logoIndex": 0, "logoPosition": {"top": 5298.5, "bottom": 5298.5}, "cardPosition": {"top": 5265.5, "bottom": 6280.75}, "isLogoOnTop": true, "textBelowLogo": true, "textElementsFound": 6, "status": "pass"}]}, "buttonTextWrapping": {"status": "pass", "details": ["All 5 buttons wrap text properly"]}, "wordHyphenation": {"status": "pass", "details": ["No unwanted hyphenation found in 177 text elements"]}, "footerElements": {"status": "pass", "details": ["Footer elements properly sized - 1 logos, 6 emails"]}}, "screenshot": "C:\\AI Coding Projects\\Clients\\wellness-marketing-maestros\\post-implementation-screenshots\\Homepage-375px-post-fix.png"}, {"breakpointName": "iPhone 12/13/14", "width": 414, "height": 896, "fixVerifications": {"marketingSherpaCard": {"status": "pass", "details": [{"logoIndex": 0, "logoPosition": {"top": 5051.75, "bottom": 5051.75}, "cardPosition": {"top": 5018.75, "bottom": 5949.5}, "isLogoOnTop": true, "textBelowLogo": true, "textElementsFound": 6, "status": "pass"}]}, "buttonTextWrapping": {"status": "pass", "details": ["All 5 buttons wrap text properly"]}, "wordHyphenation": {"status": "pass", "details": ["No unwanted hyphenation found in 177 text elements"]}, "footerElements": {"status": "pass", "details": ["Footer elements properly sized - 1 logos, 6 emails"]}}, "screenshot": "C:\\AI Coding Projects\\Clients\\wellness-marketing-maestros\\post-implementation-screenshots\\Homepage-414px-post-fix.png"}, {"breakpointName": "iPad Portrait", "width": 768, "height": 1024, "fixVerifications": {"marketingSherpaCard": {"status": "pass", "details": [{"logoIndex": 0, "logoPosition": {"top": 5340.5625, "bottom": 5340.5625}, "cardPosition": {"top": 5307.5625, "bottom": 5896.3125}, "isLogoOnTop": true, "textBelowLogo": true, "textElementsFound": 6, "status": "pass"}]}, "buttonTextWrapping": {"status": "pass", "details": ["All 5 buttons wrap text properly"]}, "wordHyphenation": {"status": "pass", "details": ["No unwanted hyphenation found in 177 text elements"]}, "footerElements": {"status": "pass", "details": ["Footer elements properly sized - 1 logos, 6 emails"]}}, "screenshot": "C:\\AI Coding Projects\\Clients\\wellness-marketing-maestros\\post-implementation-screenshots\\Homepage-768px-post-fix.png"}]}, {"pageName": "About", "url": "/about", "breakpointResults": [{"breakpointName": "iPhone SE", "width": 320, "height": 568, "fixVerifications": {"marketingSherpaCard": {"status": "not_applicable", "details": ""}, "buttonTextWrapping": {"status": "pass", "details": ["All 3 buttons wrap text properly"]}, "wordHyphenation": {"status": "pass", "details": ["No unwanted hyphenation found in 109 text elements"]}, "footerElements": {"status": "fail", "details": ["logo-overflow: Logo 1 in footer 1 - overflow detected", "email-overflow: Email 1 in footer 1 - Strategic consultancy for established wellness cli", "email-overflow: Email 2 in footer 1 - Strategic consultancy for established wellness cli", "email-overflow: Email 3 in footer 1 - Strategic consultancy for established wellness cli", "email-overflow: Email 4 in footer 1 - <EMAIL> -", "email-overflow: Email 5 in footer 1 - <EMAIL>", "email-overflow: Email 6 in footer 1 - <EMAIL>"]}}, "screenshot": "C:\\AI Coding Projects\\Clients\\wellness-marketing-maestros\\post-implementation-screenshots\\About-320px-post-fix.png"}, {"breakpointName": "iPhone 12/13 mini", "width": 375, "height": 812, "fixVerifications": {"marketingSherpaCard": {"status": "not_applicable", "details": ""}, "buttonTextWrapping": {"status": "pass", "details": ["All 3 buttons wrap text properly"]}, "wordHyphenation": {"status": "pass", "details": ["No unwanted hyphenation found in 109 text elements"]}, "footerElements": {"status": "pass", "details": ["Footer elements properly sized - 1 logos, 6 emails"]}}, "screenshot": "C:\\AI Coding Projects\\Clients\\wellness-marketing-maestros\\post-implementation-screenshots\\About-375px-post-fix.png"}, {"breakpointName": "iPhone 12/13/14", "width": 414, "height": 896, "fixVerifications": {"marketingSherpaCard": {"status": "not_applicable", "details": ""}, "buttonTextWrapping": {"status": "pass", "details": ["All 3 buttons wrap text properly"]}, "wordHyphenation": {"status": "pass", "details": ["No unwanted hyphenation found in 109 text elements"]}, "footerElements": {"status": "pass", "details": ["Footer elements properly sized - 1 logos, 6 emails"]}}, "screenshot": "C:\\AI Coding Projects\\Clients\\wellness-marketing-maestros\\post-implementation-screenshots\\About-414px-post-fix.png"}, {"breakpointName": "iPad Portrait", "width": 768, "height": 1024, "fixVerifications": {"marketingSherpaCard": {"status": "not_applicable", "details": ""}, "buttonTextWrapping": {"status": "pass", "details": ["All 3 buttons wrap text properly"]}, "wordHyphenation": {"status": "pass", "details": ["No unwanted hyphenation found in 109 text elements"]}, "footerElements": {"status": "pass", "details": ["Footer elements properly sized - 1 logos, 6 emails"]}}, "screenshot": "C:\\AI Coding Projects\\Clients\\wellness-marketing-maestros\\post-implementation-screenshots\\About-768px-post-fix.png"}]}, {"pageName": "Contact", "url": "/contact", "breakpointResults": [{"breakpointName": "iPhone SE", "width": 320, "height": 568, "fixVerifications": {"marketingSherpaCard": {"status": "not_applicable", "details": ""}, "buttonTextWrapping": {"status": "pass", "details": ["All 5 buttons wrap text properly"]}, "wordHyphenation": {"status": "pass", "details": ["No unwanted hyphenation found in 105 text elements"]}, "footerElements": {"status": "fail", "details": ["logo-overflow: Logo 1 in footer 1 - overflow detected", "email-overflow: Email 1 in footer 1 - Strategic consultancy for established wellness cli", "email-overflow: Email 2 in footer 1 - Strategic consultancy for established wellness cli", "email-overflow: Email 3 in footer 1 - Strategic consultancy for established wellness cli", "email-overflow: Email 4 in footer 1 - <EMAIL> -", "email-overflow: Email 5 in footer 1 - <EMAIL>", "email-overflow: Email 6 in footer 1 - <EMAIL>"]}}, "screenshot": "C:\\AI Coding Projects\\Clients\\wellness-marketing-maestros\\post-implementation-screenshots\\Contact-320px-post-fix.png"}, {"breakpointName": "iPhone 12/13 mini", "width": 375, "height": 812, "fixVerifications": {"marketingSherpaCard": {"status": "not_applicable", "details": ""}, "buttonTextWrapping": {"status": "pass", "details": ["All 5 buttons wrap text properly"]}, "wordHyphenation": {"status": "pass", "details": ["No unwanted hyphenation found in 105 text elements"]}, "footerElements": {"status": "pass", "details": ["Footer elements properly sized - 1 logos, 6 emails"]}}, "screenshot": "C:\\AI Coding Projects\\Clients\\wellness-marketing-maestros\\post-implementation-screenshots\\Contact-375px-post-fix.png"}, {"breakpointName": "iPhone 12/13/14", "width": 414, "height": 896, "fixVerifications": {"marketingSherpaCard": {"status": "not_applicable", "details": ""}, "buttonTextWrapping": {"status": "pass", "details": ["All 5 buttons wrap text properly"]}, "wordHyphenation": {"status": "pass", "details": ["No unwanted hyphenation found in 105 text elements"]}, "footerElements": {"status": "pass", "details": ["Footer elements properly sized - 1 logos, 6 emails"]}}, "screenshot": "C:\\AI Coding Projects\\Clients\\wellness-marketing-maestros\\post-implementation-screenshots\\Contact-414px-post-fix.png"}, {"breakpointName": "iPad Portrait", "width": 768, "height": 1024, "fixVerifications": {"marketingSherpaCard": {"status": "not_applicable", "details": ""}, "buttonTextWrapping": {"status": "pass", "details": ["All 5 buttons wrap text properly"]}, "wordHyphenation": {"status": "pass", "details": ["No unwanted hyphenation found in 105 text elements"]}, "footerElements": {"status": "pass", "details": ["Footer elements properly sized - 1 logos, 6 emails"]}}, "screenshot": "C:\\AI Coding Projects\\Clients\\wellness-marketing-maestros\\post-implementation-screenshots\\Contact-768px-post-fix.png"}]}, {"pageName": "Growth Audit", "url": "/growth-audit", "breakpointResults": [{"breakpointName": "iPhone SE", "width": 320, "height": 568, "fixVerifications": {"marketingSherpaCard": {"status": "not_applicable", "details": ""}, "buttonTextWrapping": {"status": "pass", "details": ["All 3 buttons wrap text properly"]}, "wordHyphenation": {"status": "pass", "details": ["No unwanted hyphenation found in 164 text elements"]}, "footerElements": {"status": "fail", "details": ["logo-overflow: Logo 1 in footer 1 - overflow detected", "email-overflow: Email 1 in footer 1 - Strategic consultancy for established wellness cli", "email-overflow: Email 2 in footer 1 - Strategic consultancy for established wellness cli", "email-overflow: Email 3 in footer 1 - Strategic consultancy for established wellness cli", "email-overflow: Email 4 in footer 1 - <EMAIL> -", "email-overflow: Email 5 in footer 1 - <EMAIL>", "email-overflow: Email 6 in footer 1 - <EMAIL>"]}}, "screenshot": "C:\\AI Coding Projects\\Clients\\wellness-marketing-maestros\\post-implementation-screenshots\\Growth-Audit-320px-post-fix.png"}, {"breakpointName": "iPhone 12/13 mini", "width": 375, "height": 812, "fixVerifications": {"marketingSherpaCard": {"status": "not_applicable", "details": ""}, "buttonTextWrapping": {"status": "pass", "details": ["All 3 buttons wrap text properly"]}, "wordHyphenation": {"status": "pass", "details": ["No unwanted hyphenation found in 164 text elements"]}, "footerElements": {"status": "pass", "details": ["Footer elements properly sized - 1 logos, 6 emails"]}}, "screenshot": "C:\\AI Coding Projects\\Clients\\wellness-marketing-maestros\\post-implementation-screenshots\\Growth-Audit-375px-post-fix.png"}, {"breakpointName": "iPhone 12/13/14", "width": 414, "height": 896, "fixVerifications": {"marketingSherpaCard": {"status": "not_applicable", "details": ""}, "buttonTextWrapping": {"status": "pass", "details": ["All 3 buttons wrap text properly"]}, "wordHyphenation": {"status": "pass", "details": ["No unwanted hyphenation found in 164 text elements"]}, "footerElements": {"status": "pass", "details": ["Footer elements properly sized - 1 logos, 6 emails"]}}, "screenshot": "C:\\AI Coding Projects\\Clients\\wellness-marketing-maestros\\post-implementation-screenshots\\Growth-Audit-414px-post-fix.png"}, {"breakpointName": "iPad Portrait", "width": 768, "height": 1024, "fixVerifications": {"marketingSherpaCard": {"status": "not_applicable", "details": ""}, "buttonTextWrapping": {"status": "pass", "details": ["All 3 buttons wrap text properly"]}, "wordHyphenation": {"status": "pass", "details": ["No unwanted hyphenation found in 164 text elements"]}, "footerElements": {"status": "pass", "details": ["Footer elements properly sized - 1 logos, 6 emails"]}}, "screenshot": "C:\\AI Coding Projects\\Clients\\wellness-marketing-maestros\\post-implementation-screenshots\\Growth-Audit-768px-post-fix.png"}]}, {"pageName": "Core Implementation", "url": "/core-implementation", "breakpointResults": [{"breakpointName": "iPhone SE", "width": 320, "height": 568, "fixVerifications": {"marketingSherpaCard": {"status": "not_applicable", "details": ""}, "buttonTextWrapping": {"status": "pass", "details": ["All 3 buttons wrap text properly"]}, "wordHyphenation": {"status": "pass", "details": ["No unwanted hyphenation found in 169 text elements"]}, "footerElements": {"status": "fail", "details": ["logo-overflow: Logo 1 in footer 1 - overflow detected", "email-overflow: Email 1 in footer 1 - Strategic consultancy for established wellness cli", "email-overflow: Email 2 in footer 1 - Strategic consultancy for established wellness cli", "email-overflow: Email 3 in footer 1 - Strategic consultancy for established wellness cli", "email-overflow: Email 4 in footer 1 - <EMAIL> -", "email-overflow: Email 5 in footer 1 - <EMAIL>", "email-overflow: Email 6 in footer 1 - <EMAIL>"]}}, "screenshot": "C:\\AI Coding Projects\\Clients\\wellness-marketing-maestros\\post-implementation-screenshots\\Core-Implementation-320px-post-fix.png"}, {"breakpointName": "iPhone 12/13 mini", "width": 375, "height": 812, "fixVerifications": {"marketingSherpaCard": {"status": "not_applicable", "details": ""}, "buttonTextWrapping": {"status": "pass", "details": ["All 3 buttons wrap text properly"]}, "wordHyphenation": {"status": "pass", "details": ["No unwanted hyphenation found in 169 text elements"]}, "footerElements": {"status": "pass", "details": ["Footer elements properly sized - 1 logos, 6 emails"]}}, "screenshot": "C:\\AI Coding Projects\\Clients\\wellness-marketing-maestros\\post-implementation-screenshots\\Core-Implementation-375px-post-fix.png"}, {"breakpointName": "iPhone 12/13/14", "width": 414, "height": 896, "fixVerifications": {"marketingSherpaCard": {"status": "not_applicable", "details": ""}, "buttonTextWrapping": {"status": "pass", "details": ["All 3 buttons wrap text properly"]}, "wordHyphenation": {"status": "pass", "details": ["No unwanted hyphenation found in 169 text elements"]}, "footerElements": {"status": "pass", "details": ["Footer elements properly sized - 1 logos, 6 emails"]}}, "screenshot": "C:\\AI Coding Projects\\Clients\\wellness-marketing-maestros\\post-implementation-screenshots\\Core-Implementation-414px-post-fix.png"}, {"breakpointName": "iPad Portrait", "width": 768, "height": 1024, "fixVerifications": {"marketingSherpaCard": {"status": "not_applicable", "details": ""}, "buttonTextWrapping": {"status": "pass", "details": ["All 3 buttons wrap text properly"]}, "wordHyphenation": {"status": "pass", "details": ["No unwanted hyphenation found in 169 text elements"]}, "footerElements": {"status": "pass", "details": ["Footer elements properly sized - 1 logos, 6 emails"]}}, "screenshot": "C:\\AI Coding Projects\\Clients\\wellness-marketing-maestros\\post-implementation-screenshots\\Core-Implementation-768px-post-fix.png"}]}, {"pageName": "Wellness Clinics", "url": "/wellness-clinics", "breakpointResults": [{"breakpointName": "iPhone SE", "width": 320, "height": 568, "fixVerifications": {"marketingSherpaCard": {"status": "not_applicable", "details": ""}, "buttonTextWrapping": {"status": "pass", "details": ["All 7 buttons wrap text properly"]}, "wordHyphenation": {"status": "pass", "details": ["No unwanted hyphenation found in 138 text elements"]}, "footerElements": {"status": "fail", "details": ["logo-overflow: Logo 1 in footer 1 - overflow detected", "email-overflow: Email 1 in footer 1 - Strategic consultancy for established wellness cli", "email-overflow: Email 2 in footer 1 - Strategic consultancy for established wellness cli", "email-overflow: Email 3 in footer 1 - Strategic consultancy for established wellness cli", "email-overflow: Email 4 in footer 1 - <EMAIL> -", "email-overflow: Email 5 in footer 1 - <EMAIL>", "email-overflow: Email 6 in footer 1 - <EMAIL>"]}}, "screenshot": "C:\\AI Coding Projects\\Clients\\wellness-marketing-maestros\\post-implementation-screenshots\\Wellness-Clinics-320px-post-fix.png"}, {"breakpointName": "iPhone 12/13 mini", "width": 375, "height": 812, "fixVerifications": {"marketingSherpaCard": {"status": "not_applicable", "details": ""}, "buttonTextWrapping": {"status": "pass", "details": ["All 7 buttons wrap text properly"]}, "wordHyphenation": {"status": "pass", "details": ["No unwanted hyphenation found in 138 text elements"]}, "footerElements": {"status": "pass", "details": ["Footer elements properly sized - 1 logos, 6 emails"]}}, "screenshot": "C:\\AI Coding Projects\\Clients\\wellness-marketing-maestros\\post-implementation-screenshots\\Wellness-Clinics-375px-post-fix.png"}, {"breakpointName": "iPhone 12/13/14", "width": 414, "height": 896, "fixVerifications": {"marketingSherpaCard": {"status": "not_applicable", "details": ""}, "buttonTextWrapping": {"status": "pass", "details": ["All 7 buttons wrap text properly"]}, "wordHyphenation": {"status": "pass", "details": ["No unwanted hyphenation found in 138 text elements"]}, "footerElements": {"status": "pass", "details": ["Footer elements properly sized - 1 logos, 6 emails"]}}, "screenshot": "C:\\AI Coding Projects\\Clients\\wellness-marketing-maestros\\post-implementation-screenshots\\Wellness-Clinics-414px-post-fix.png"}, {"breakpointName": "iPad Portrait", "width": 768, "height": 1024, "fixVerifications": {"marketingSherpaCard": {"status": "not_applicable", "details": ""}, "buttonTextWrapping": {"status": "pass", "details": ["All 7 buttons wrap text properly"]}, "wordHyphenation": {"status": "pass", "details": ["No unwanted hyphenation found in 138 text elements"]}, "footerElements": {"status": "pass", "details": ["Footer elements properly sized - 1 logos, 6 emails"]}}, "screenshot": "C:\\AI Coding Projects\\Clients\\wellness-marketing-maestros\\post-implementation-screenshots\\Wellness-Clinics-768px-post-fix.png"}]}, {"pageName": "Medical Spas", "url": "/medical-spas", "breakpointResults": [{"breakpointName": "iPhone SE", "width": 320, "height": 568, "fixVerifications": {"marketingSherpaCard": {"status": "not_applicable", "details": ""}, "buttonTextWrapping": {"status": "pass", "details": ["All 7 buttons wrap text properly"]}, "wordHyphenation": {"status": "pass", "details": ["No unwanted hyphenation found in 138 text elements"]}, "footerElements": {"status": "fail", "details": ["logo-overflow: Logo 1 in footer 1 - overflow detected", "email-overflow: Email 1 in footer 1 - Strategic consultancy for established wellness cli", "email-overflow: Email 2 in footer 1 - Strategic consultancy for established wellness cli", "email-overflow: Email 3 in footer 1 - Strategic consultancy for established wellness cli", "email-overflow: Email 4 in footer 1 - <EMAIL> -", "email-overflow: Email 5 in footer 1 - <EMAIL>", "email-overflow: Email 6 in footer 1 - <EMAIL>"]}}, "screenshot": "C:\\AI Coding Projects\\Clients\\wellness-marketing-maestros\\post-implementation-screenshots\\Medical-Spas-320px-post-fix.png"}, {"breakpointName": "iPhone 12/13 mini", "width": 375, "height": 812, "fixVerifications": {"marketingSherpaCard": {"status": "not_applicable", "details": ""}, "buttonTextWrapping": {"status": "pass", "details": ["All 7 buttons wrap text properly"]}, "wordHyphenation": {"status": "pass", "details": ["No unwanted hyphenation found in 138 text elements"]}, "footerElements": {"status": "pass", "details": ["Footer elements properly sized - 1 logos, 6 emails"]}}, "screenshot": "C:\\AI Coding Projects\\Clients\\wellness-marketing-maestros\\post-implementation-screenshots\\Medical-Spas-375px-post-fix.png"}, {"breakpointName": "iPhone 12/13/14", "width": 414, "height": 896, "fixVerifications": {"marketingSherpaCard": {"status": "not_applicable", "details": ""}, "buttonTextWrapping": {"status": "pass", "details": ["All 7 buttons wrap text properly"]}, "wordHyphenation": {"status": "pass", "details": ["No unwanted hyphenation found in 138 text elements"]}, "footerElements": {"status": "pass", "details": ["Footer elements properly sized - 1 logos, 6 emails"]}}, "screenshot": "C:\\AI Coding Projects\\Clients\\wellness-marketing-maestros\\post-implementation-screenshots\\Medical-Spas-414px-post-fix.png"}, {"breakpointName": "iPad Portrait", "width": 768, "height": 1024, "fixVerifications": {"marketingSherpaCard": {"status": "not_applicable", "details": ""}, "buttonTextWrapping": {"status": "pass", "details": ["All 7 buttons wrap text properly"]}, "wordHyphenation": {"status": "pass", "details": ["No unwanted hyphenation found in 138 text elements"]}, "footerElements": {"status": "pass", "details": ["Footer elements properly sized - 1 logos, 6 emails"]}}, "screenshot": "C:\\AI Coding Projects\\Clients\\wellness-marketing-maestros\\post-implementation-screenshots\\Medical-Spas-768px-post-fix.png"}]}, {"pageName": "Holistic Functional Medicine", "url": "/holistic-functional-medicine", "breakpointResults": [{"breakpointName": "iPhone SE", "width": 320, "height": 568, "fixVerifications": {"marketingSherpaCard": {"status": "not_applicable", "details": ""}, "buttonTextWrapping": {"status": "pass", "details": ["All 7 buttons wrap text properly"]}, "wordHyphenation": {"status": "pass", "details": ["No unwanted hyphenation found in 138 text elements"]}, "footerElements": {"status": "fail", "details": ["logo-overflow: Logo 1 in footer 1 - overflow detected", "email-overflow: Email 1 in footer 1 - Strategic consultancy for established wellness cli", "email-overflow: Email 2 in footer 1 - Strategic consultancy for established wellness cli", "email-overflow: Email 3 in footer 1 - Strategic consultancy for established wellness cli", "email-overflow: Email 4 in footer 1 - <EMAIL> -", "email-overflow: Email 5 in footer 1 - <EMAIL>", "email-overflow: Email 6 in footer 1 - <EMAIL>"]}}, "screenshot": "C:\\AI Coding Projects\\Clients\\wellness-marketing-maestros\\post-implementation-screenshots\\Holistic-Functional-Medicine-320px-post-fix.png"}, {"breakpointName": "iPhone 12/13 mini", "width": 375, "height": 812, "fixVerifications": {"marketingSherpaCard": {"status": "not_applicable", "details": ""}, "buttonTextWrapping": {"status": "pass", "details": ["All 7 buttons wrap text properly"]}, "wordHyphenation": {"status": "pass", "details": ["No unwanted hyphenation found in 138 text elements"]}, "footerElements": {"status": "pass", "details": ["Footer elements properly sized - 1 logos, 6 emails"]}}, "screenshot": "C:\\AI Coding Projects\\Clients\\wellness-marketing-maestros\\post-implementation-screenshots\\Holistic-Functional-Medicine-375px-post-fix.png"}, {"breakpointName": "iPhone 12/13/14", "width": 414, "height": 896, "fixVerifications": {"marketingSherpaCard": {"status": "not_applicable", "details": ""}, "buttonTextWrapping": {"status": "pass", "details": ["All 7 buttons wrap text properly"]}, "wordHyphenation": {"status": "pass", "details": ["No unwanted hyphenation found in 138 text elements"]}, "footerElements": {"status": "pass", "details": ["Footer elements properly sized - 1 logos, 6 emails"]}}, "screenshot": "C:\\AI Coding Projects\\Clients\\wellness-marketing-maestros\\post-implementation-screenshots\\Holistic-Functional-Medicine-414px-post-fix.png"}, {"breakpointName": "iPad Portrait", "width": 768, "height": 1024, "fixVerifications": {"marketingSherpaCard": {"status": "not_applicable", "details": ""}, "buttonTextWrapping": {"status": "pass", "details": ["All 7 buttons wrap text properly"]}, "wordHyphenation": {"status": "pass", "details": ["No unwanted hyphenation found in 138 text elements"]}, "footerElements": {"status": "pass", "details": ["Footer elements properly sized - 1 logos, 6 emails"]}}, "screenshot": "C:\\AI Coding Projects\\Clients\\wellness-marketing-maestros\\post-implementation-screenshots\\Holistic-Functional-Medicine-768px-post-fix.png"}]}, {"pageName": "Multi-practitioner Centers", "url": "/multi-practitioner-centers", "breakpointResults": [{"breakpointName": "iPhone SE", "width": 320, "height": 568, "fixVerifications": {"marketingSherpaCard": {"status": "not_applicable", "details": ""}, "buttonTextWrapping": {"status": "pass", "details": ["All 7 buttons wrap text properly"]}, "wordHyphenation": {"status": "pass", "details": ["No unwanted hyphenation found in 138 text elements"]}, "footerElements": {"status": "fail", "details": ["logo-overflow: Logo 1 in footer 1 - overflow detected", "email-overflow: Email 1 in footer 1 - Strategic consultancy for established wellness cli", "email-overflow: Email 2 in footer 1 - Strategic consultancy for established wellness cli", "email-overflow: Email 3 in footer 1 - Strategic consultancy for established wellness cli", "email-overflow: Email 4 in footer 1 - <EMAIL> -", "email-overflow: Email 5 in footer 1 - <EMAIL>", "email-overflow: Email 6 in footer 1 - <EMAIL>"]}}, "screenshot": "C:\\AI Coding Projects\\Clients\\wellness-marketing-maestros\\post-implementation-screenshots\\Multi-practitioner-Centers-320px-post-fix.png"}, {"breakpointName": "iPhone 12/13 mini", "width": 375, "height": 812, "fixVerifications": {"marketingSherpaCard": {"status": "not_applicable", "details": ""}, "buttonTextWrapping": {"status": "pass", "details": ["All 7 buttons wrap text properly"]}, "wordHyphenation": {"status": "pass", "details": ["No unwanted hyphenation found in 138 text elements"]}, "footerElements": {"status": "pass", "details": ["Footer elements properly sized - 1 logos, 6 emails"]}}, "screenshot": "C:\\AI Coding Projects\\Clients\\wellness-marketing-maestros\\post-implementation-screenshots\\Multi-practitioner-Centers-375px-post-fix.png"}, {"breakpointName": "iPhone 12/13/14", "width": 414, "height": 896, "fixVerifications": {"marketingSherpaCard": {"status": "not_applicable", "details": ""}, "buttonTextWrapping": {"status": "pass", "details": ["All 7 buttons wrap text properly"]}, "wordHyphenation": {"status": "pass", "details": ["No unwanted hyphenation found in 138 text elements"]}, "footerElements": {"status": "pass", "details": ["Footer elements properly sized - 1 logos, 6 emails"]}}, "screenshot": "C:\\AI Coding Projects\\Clients\\wellness-marketing-maestros\\post-implementation-screenshots\\Multi-practitioner-Centers-414px-post-fix.png"}, {"breakpointName": "iPad Portrait", "width": 768, "height": 1024, "fixVerifications": {"marketingSherpaCard": {"status": "not_applicable", "details": ""}, "buttonTextWrapping": {"status": "pass", "details": ["All 7 buttons wrap text properly"]}, "wordHyphenation": {"status": "pass", "details": ["No unwanted hyphenation found in 138 text elements"]}, "footerElements": {"status": "pass", "details": ["Footer elements properly sized - 1 logos, 6 emails"]}}, "screenshot": "C:\\AI Coding Projects\\Clients\\wellness-marketing-maestros\\post-implementation-screenshots\\Multi-practitioner-Centers-768px-post-fix.png"}]}]}